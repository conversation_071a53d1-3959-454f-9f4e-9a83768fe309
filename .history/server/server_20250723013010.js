import express from 'express';
import bodyParser from 'body-parser';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import cors from 'cors';
import dotenv from 'dotenv';

// Dynamic IP Detection
import { getSmartIP } from './modules/helpers.js';

// API Routes
// import { fastifyHandler } from './api/v2/index.js'; // Temporarily disabled
import { registerWebhookListeners } from './modules/webhooks/registerEventListeners.js';
import { setupWebSocketServer } from './modules/socket/index.js';
import './modules/webhooks/playground.js';

// Temporarily removing all router imports to test basic server
// import SiteChat from './modules/chat/index.js';
// import SiteApiRouter from './modules/site.js';
// import TestApiRouter from './modules/test_api.js';
// import TestApiRouter2 from './modules/test_api2.js';
// import AdminApiRouter from './modules/api_admin.js';
// import AdminApiRouter2 from './modules/api_admin2.js';
// import apiRouter from './modules/api.js';
// import ApiAzureRouter from './modules/api_azure.js';
// import apiUploader from './modules/api_uploader.js';
// import apiWebhooks from './modules/webhooks/api_webhooks.js';

// Error Handling
process.on('uncaughtException', (err) => console.error('❌ Uncaught:', err));
process.on('unhandledRejection', (reason) => console.error('❌ Unhandled:', reason));

// Initialize Webhook Listeners
registerWebhookListeners();

const app = express();
const startTime = Date.now(); // Track server startup time

// Environment & Configuration
dotenv.config({ path: path.join(path.dirname(fileURLToPath(import.meta.url)), '.env') });
const PORT = process.env.PORT || 9002;
export const isDev = process.env.NODE_ENV !== 'production';
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Vite-style console logging
const colors = isDev ? {
	green: '\x1b[32m',
	cyan: '\x1b[36m',
	blue: '\x1b[34m',
	magenta: '\x1b[35m',
	reset: '\x1b[0m',
	bold: '\x1b[1m',
	dim: '\x1b[2m'
} : {};

const log = {
	success: (msg) => console.log(isDev ? `${colors.green}✓${colors.reset} ${msg}` : msg),
	server: (name, version, readyTime) => {
		if (isDev) {
			console.log(`\n${colors.cyan}${colors.bold}  ${name}${colors.reset} ${colors.green}v${version}${colors.reset}  ${colors.dim}ready in ${readyTime} ms${colors.reset}\n`);
		} else {
			console.log(`${name} v${version} ready in ${readyTime} ms`);
		}
	},
	network: (label, url) => {
		if (isDev) {
			console.log(`  ${colors.green}➜${colors.reset}  ${colors.bold}${label}:${colors.reset}   ${colors.cyan}${url}${colors.reset}`);
		} else {
			console.log(`${label}: ${url}`);
		}
	},
	ready: (msg) => {
		if (isDev) {
			console.log(`${colors.green}${colors.bold}${msg}${colors.reset}\n`);
		} else {
			console.log(msg);
		}
	}
};

// Middleware
app.use(cors());
app.set('trust proxy', 1);
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true, parameterLimit: 50000 }));

// API Routes - Temporarily disabled to test basic server
// const syncRoutes = [
// 	{ path: '/api_chat', router: SiteChat },
// 	{ path: '/api_site', router: SiteApiRouter },
// 	{ path: '/api_test', router: TestApiRouter },
// 	{ path: '/api_test2', router: TestApiRouter2 },
// 	{ path: '/api_admin', router: AdminApiRouter },
// 	{ path: '/api_admin2', router: AdminApiRouter2 },
// 	{ path: '/api', router: apiRouter },
// 	{ path: '/api_uploader', router: apiUploader },
// 	{ path: '/api_azure', router: ApiAzureRouter },
// 	{ path: '/api_webhooks', router: apiWebhooks },
// ];

// syncRoutes.forEach(({ path, router }) => app.use(path, router));

// Initialize async routes
async function initializeAsyncRoutes() {
	try {
		// Temporarily disabled to isolate router initialization issue
		// const v2Router = await fastifyHandler();
		// app.use('/v2', v2Router);
		console.log('✅ Async routes initialization skipped for debugging');
	} catch (error) {
		console.error('Failed to initialize v2 API routes:', error);
	}
}

// Route Handlers
app.get('/', (req, res) => res.redirect('/admin'));
app.get('/api-info', (req, res) => {
	res.json({
		status: 'success',
		message: 'CEPA Test Platform API Server',
		version: '2.0.0',
		timestamp: new Date().toISOString(),
		endpoints: { admin: '/admin', api: '/api', test: '/test', websocket: '/api_webhooks/socket' }
	});
});

// Static File Serving
const staticHandler = (dir, route = dir) => {
	const distPath = path.join(__dirname, `../${dir}/dist`);
	app.use(`/${route}`, express.static(distPath));
	// Catch-all route for SPA
	app.get(`/${route}/*`, (req, res) => {
		res.sendFile(path.resolve(distPath, 'index.html'));
	});
};

// Temporarily disable static handlers to isolate the issue
// staticHandler('test');
// staticHandler('admin');

// Server Setup and Start
async function startServer() {
	// Initialize async routes first
	await initializeAsyncRoutes();

	const currentIP = isDev ? await getSmartIP() : '0.0.0.0';
	const { createServer: createHttpServer } = await import('http');
	let httpServer = createHttpServer(app);

	// WebSocket Setup
	setupWebSocketServer(httpServer);

	// Start Server
	if (isDev) {
		// Development: HTTP only
		httpServer.listen(PORT, '0.0.0.0', () => {
			const readyTime = Date.now() - startTime;
			log.server('CEPA', '2.0.0', readyTime);
			log.network('Local', `http://localhost:${PORT}/`);
			log.network('Network', `http://${currentIP}:${PORT}/`);
		});
	} else {
		// Production
		httpServer.listen(PORT, '0.0.0.0', () => {
			const readyTime = Date.now() - startTime;
			log.success(`CEPA v2.0.0 ready in ${readyTime} ms - http://0.0.0.0:${PORT}`);
		});
	}
}

// Start the server
startServer().catch(error => {
	console.error('💥 Failed to start server:', error);
	process.exit(1);
});

{"name": "cepatest_backend", "version": "1.0.0", "description": "CEPA Test Node.js Server Backend Apps", "type": "module", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "start:check": "node scripts/start-with-check.js", "dev": "OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES NODE_ENV=development nodemon --trace-deprecation scripts/start-dev.js", "sync": "node scripts/sync-database.js"}, "author": "", "license": "ISC", "devDependencies": {"nodemon": "^2.0.22"}, "dependencies": {"@fastify/express": "^4.0.2", "@fastify/rate-limit": "^10.3.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "*", "express-rate-limit": "^8.0.1", "fastify": "^5.4.0", "firebase-admin": "^13.4.0", "fs": "^0.0.1-security", "https": "^1.0.0", "jsonwebtoken": "^9.0.2", "moment-timezone": "^0.6.0", "multer": "^1.4.5-lts.1", "mysql": "^2.18.1", "next": "^13.4.12", "node-fetch": "^3.3.2", "path": "^0.12.7", "react": "^18.2.0", "react-dom": "^17.0.2", "request-ip": "^3.3.0", "socket.io": "^4.6.1"}}